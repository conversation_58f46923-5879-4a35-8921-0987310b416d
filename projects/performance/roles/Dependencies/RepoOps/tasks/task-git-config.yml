---
- name: Check if ansible repository directory exists
  stat:
    path: "{{ PERF.home_path }}/{{ PERF.repo.ansible.repo_root_dir }}/{{ PERF.repo.ansible.repo_name }}"
  register: ansible_repo_dir
  delegate_to: localhost

- name: Check if ansible repository is a git repository
  shell: "cd {{ PERF.home_path }}/{{ PERF.repo.ansible.repo_root_dir }}/{{ PERF.repo.ansible.repo_name }} && git rev-parse --is-inside-work-tree"
  register: is_git_repo
  changed_when: false
  failed_when: false
  delegate_to: localhost
  when: ansible_repo_dir.stat.exists

- name: Configure core.filemode status
  git_config:
    name: core.filemode
    value: "true"
    repo: "{{ PERF.home_path }}/{{ PERF.repo.ansible.repo_root_dir }}/{{ PERF.repo.ansible.repo_name }}"
    scope: local
  changed_when: true
  ignore_errors: true
  register: filemode_config
  delegate_to: localhost
  when: ansible_repo_dir.stat.exists and is_git_repo.rc == 0

- name: Verify core.filemode configuration
  debug:
    msg: "Failed to configure core.filemode"
  when: filemode_config is defined and filemode_config is failed
  delegate_to: localhost

- name: Configure .githooks to core.hooksPath
  git_config:
    name: core.hooksPath
    value: ".githooks"
    repo: "{{ PERF.home_path }}/{{ PERF.repo.ansible.repo_root_dir }}/{{ PERF.repo.ansible.repo_name }}"
    scope: local
  changed_when: true
  ignore_errors: true
  register: hooks_config
  delegate_to: localhost
  when: ansible_repo_dir.stat.exists and is_git_repo.rc == 0

- name: Verify core.hooksPath configuration
  debug:
    msg: "Failed to configure core.hooksPath"
  when: hooks_config is defined and hooks_config is failed
  delegate_to: localhost

- name: Skip git configuration when repository does not exist or is not a git repository
  debug:
    msg: "Skipping git configuration because {{ PERF.home_path }}/{{ PERF.repo.ansible.repo_root_dir }}/{{ PERF.repo.ansible.repo_name }} is not a valid git repository"
  when: not ansible_repo_dir.stat.exists or (is_git_repo is defined and is_git_repo.rc != 0)
  delegate_to: localhost
