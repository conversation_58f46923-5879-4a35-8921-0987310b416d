---
- name: "< Standalone >: start locust load test"
  block:
    - name: Prepare project before starting locust if needed
      block:
        - name: Sync project files locally on target node (using rsync command)
          shell: >
            rsync -av --verbose
            {{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}/{{ standalone.scenario_data.relative_src_dir }}/
            {{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}/{{ standalone.scenario_data.relative_dest_dir }}/
          ignore_errors: true

        - name: Define and register locust project env dir path
          set_fact:
            project_env_dir: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}/{{ standalone.scenario_data.relative_dest_dir }}/env"

        - name: Copy docker-compose files to target path
          copy:
            src: "{{ project_env_dir }}/{{ item }}"
            dest: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}"
            owner: "{{ ansible_user }}"
            group: "{{ ansible_user }}"
            mode: "0664"
            force: true
            remote_src: true
          loop: "{{ standalone.docker_compose_files | default([]) }}"
      when: standalone.sync_data | bool

    - name: Build locust image before starting locust if needed
      block:
        - name: Update BuildImage.sh executable permission
          file:
            path: "{{ project_env_dir }}/BuildImage.sh"
            owner: "{{ ansible_user }}"
            group: "{{ ansible_user }}"
            mode: "0755"

        - name: Build locust image
          command: "bash BuildImage.sh"
          args:
            chdir: "{{ project_env_dir }}"
          register: build_result
          changed_when: "'Successfully built' in build_result.stdout"
      when: standalone.build_image | bool

    - name: Start locust
      command: "docker-compose up -d"
      args:
        chdir: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}"
      register: compose_result
      changed_when: "'done' in compose_result.stdout or 'up-to-date' in compose_result.stdout"

    - name: Check if locust container is running
      command: "docker ps -q -f name={{ PERF.locust.scenarios.container_name }}"
      register: container_check
      changed_when: false
      failed_when: container_check.stdout == ""

    - name: Display locust container logs if needed
      block:
        - name: Get current date
          local_action: shell date +%Y-%m-%d
          register: current_date
          changed_when: false

        - name: Define locust log file path
          set_fact:
            locust_log_file: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}/{{ standalone.scenario_data.relative_dest_dir }}/{{ standalone.scenario_data.relative_exec_scene_dir }}/run.log"
            internal_log_file: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}/{{ standalone.scenario_data.relative_dest_dir }}/{{ standalone.scenario_data.relative_exec_scene_dir }}/internal_{{ current_date.stdout }}.log"

        - name: Check if locust container exists
          shell: "docker ps -a | grep {{ PERF.locust.scenarios.container_name }}"
          register: container_exists
          ignore_errors: true
          changed_when: false

        - name: Display container existence status
          debug:
            msg: "Container exists: {{ container_exists.rc == 0 }}"

        - name: Get container status
          shell: "docker inspect -f '{% raw %}{{.State.Status}}{% endraw %}' {{ PERF.locust.scenarios.container_name }}"
          register: container_status
          ignore_errors: true
          changed_when: false
          when: container_exists.rc == 0

        - name: Display container status
          debug:
            msg: "Container status: {{ container_status.stdout | default('Unknown') }}"
          when: container_exists.rc == 0 and container_status is defined

        - name: Check locust container logs
          command: "docker logs {{ PERF.locust.scenarios.container_name }}"
          register: container_logs
          ignore_errors: true

        - name: Display container logs
          debug:
            var: container_logs.stdout_lines

        - name: Display container logs error
          debug:
            var: container_logs.stderr_lines
          when: container_logs.stderr_lines is defined and container_logs.stderr_lines | length > 0

        - name: Check if log file exists
          stat:
            path: "{{ locust_log_file }}"
          register: log_file_stat
          ignore_errors: true

        - name: Display log file existence status
          debug:
            msg: "Log file exists: {{ log_file_stat.stat.exists | default(false) }}"

        - name: Create log file if it does not exist
          file:
            path: "{{ locust_log_file }}"
            state: touch
            mode: "0755"
            owner: "{{ ansible_user }}"
            group: "{{ ansible_user }}"
          when: not log_file_stat.stat.exists | default(false)
          ignore_errors: true

        - name: Write to log file if it does not exist
          copy:
            content: "Locust run log started at {{ ansible_date_time.iso8601 }}\n"
            dest: "{{ locust_log_file }}"
            owner: "{{ ansible_user }}"
            group: "{{ ansible_user }}"
            mode: "0644"
          when: not log_file_stat.stat.exists | default(false)
          ignore_errors: true

        - name: Read log file content
          shell: "tail -n 100 {{ locust_log_file }}"
          register: log_file_content
          ignore_errors: true
          changed_when: false

        - name: Display log file content
          debug:
            msg: "=== Locust Run Log ({{ locust_log_file }}) ==="

        - name: Display log file content
          debug:
            var: log_file_content.stdout_lines

        - name: Check if internal log file exists
          stat:
            path: "{{ internal_log_file }}"
          register: internal_log_file_stat
          ignore_errors: true

        - name: Display internal log file existence status
          debug:
            msg: "Internal log file exists: {{ internal_log_file_stat.stat.exists | default(false) }}"

        - name: Create internal log file if it does not exist
          file:
            path: "{{ internal_log_file }}"
            state: touch
            mode: "0755"
            owner: "{{ ansible_user }}"
            group: "{{ ansible_user }}"
          when: not internal_log_file_stat.stat.exists | default(false)
          ignore_errors: true

        - name: Write to internal log file if it does not exist
          copy:
            content: "Locust internal log started at {{ ansible_date_time.iso8601 }}\n"
            dest: "{{ internal_log_file }}"
            owner: "{{ ansible_user }}"
            group: "{{ ansible_user }}"
            mode: "0644"
          when: not internal_log_file_stat.stat.exists | default(false)
          ignore_errors: true

        - name: Read internal log file content
          shell: "tail -n 100 {{ internal_log_file }}"
          register: internal_log_file_content
          ignore_errors: true
          changed_when: false

        - name: Display internal log file content
          debug:
            msg: "=== Locust Internal Log ({{ internal_log_file }}) ==="

        - name: Display internal log file content
          debug:
            var: internal_log_file_content.stdout_lines

        - name: Set up log monitoring
          shell: |
            (while true; do
              echo "=== Locust Run Log ($(date)) ==="
              tail -n 100 {{ locust_log_file }}
              echo "\n=== Locust Internal Log ($(date)) ==="
              tail -n 100 {{ internal_log_file }}
              sleep 5
            done) &
          async: 300
          poll: 0
          ignore_errors: true
          changed_when: false
          when: log_file_stat.stat.exists | default(false) and internal_log_file_stat.stat.exists | default(false)
      when: standalone.debug | bool
  when: "'start' in run_type | lower"

- name: "< Standalone >: Stop locust load test"
  block:
    - name: Stop locust
      command: "docker-compose down"
      args:
        chdir: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}"
      register: compose_down_result
      changed_when: "'done' in compose_down_result.stdout or 'not found' in compose_down_result.stdout"

    - name: Check if locust container is stopped
      command: "docker ps -q -f name=locust"
      register: container_check
      changed_when: false
      failed_when: container_check.stdout != ""

    - name: Display running container status
      debug:
        msg: "Locust container is stopped (no running container found)."
      when: container_check.stdout == ""

    - name: Check if locust container is completely removed
      command: "docker ps -a -q -f name={{ PERF.locust.scenarios.container_name }}"
      register: container_all_check
      changed_when: false
      failed_when: container_all_check.stdout != ""

    - name: Display removal status
      debug:
        msg: "Locust container is completely removed."
      when: container_all_check.stdout == ""
  when: "'stop' in run_type | lower"

- name: "< Standalone >: Backup locust results"
  block:
    - name: Define and register backup dir and file paths
      set_fact:
        backup_src_dir: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}/{{ standalone.scenario_data.relative_dest_dir }}/{{ standalone.scenario_data.relative_exec_scene_dir }}/{{ standalone.backup.relative_src_dir }}"
        backup_dest_dir: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}/{{ standalone.scenario_data.relative_dest_dir }}/{{ standalone.scenario_data.relative_exec_scene_dir }}/{{ standalone.backup.relative_tar_dir }}"

    - name: Ensure backup directory exists
      file:
        path: "{{ backup_dest_dir }}"
        state: directory
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "0755"

    - name: Get current timestamp
      local_action: shell date +%Y_%m_%d-%H_%M_%S
      register: timestamp
      changed_when: false

    - name: Assert timestamp was generated
      assert:
        that:
          - timestamp.stdout | length > 0
          - timestamp.rc == 0
        fail_msg: "Failed to generate timestamp: {{ timestamp.stderr | default('No error output') }}"

    - name: Create timestamped backup directory
      file:
        path: "{{ backup_dest_dir }}/Result-{{ timestamp.stdout }}"
        state: directory
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "0755"

    - name: Copy results to backup directory
      copy:
        src: "{{ backup_src_dir }}/"
        dest: "{{ backup_dest_dir }}/Result-{{ timestamp.stdout }}/"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "0644"
        remote_src: true
      register: copy_result

    - name: Assert files were copied successfully
      assert:
        that:
          - copy_result.changed or copy_result.skipped
          - copy_result.failed is not defined or not copy_result.failed
        fail_msg: "Failed to copy results to backup directory: {{ copy_result.msg | default('Unknown error') }}"
  when: "'backup' in run_type | lower"
