---
- name: Check locustio/locust tag
  shell: "docker images locustio/locust:{{ PERF.locust.conda.locust_version }} --format '{% raw %}{{.Repository}}:{{.Tag}}{% endraw %}'"
  register: locustio_image_check
  changed_when: false
  failed_when: false

- name: Pull locustio/locust image if tag does not match
  docker_image:
    name: locustio/locust
    tag: "{{ PERF.locust.conda.locust_version }}"
    source: pull
  register: pull_result
  when: locustio_image_check.stdout == ""

# - name: Check neox-locust tag
#   shell: "docker images | grep 'neox-locust' | grep {{ PERF.locust.image.neox-locust }}"
#   register: neox_image_check
#   changed_when: false
#   failed_when: false

# - name: Build neox-locust image if tag does not match
#   shell:
#     chdir: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}/{{ PERF.locust.image.relative_dir_path_in_repo }}"
#     cmd: "bash BuildImage.sh"
#   when:
#     - neox_image_check.rc != 0
#     - pull_result.failed == false

- name: Create locust docker compose directory
  file:
    path: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}"
    state: directory
    mode: "0755"
    recurse: true
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
