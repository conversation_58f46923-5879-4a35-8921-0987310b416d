---
- name: Check if conda is installed
  shell: conda --version
  register: conda_installed
  ignore_errors: true
  changed_when: false

- name: Install miniconda3
  block:
    - name: Transport auto-install-miniconda3.sh script
      template:
        src: "{{ item }}.j2"
        dest: "{{ PERF.home_path }}/{{ item }}"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "0664"
        backup: true
      loop:
        - auto-install-miniconda3.sh
      register: transport_result
    - name: Execute miniconda3 installation script
      shell:
        cmd: "bash {{ PERF.home_path }}/auto-install-miniconda3.sh"
        chdir: "{{ PERF.home_path }}"
      when: transport_result is success
    - name: Ensure miniconda3 directory permissions and ownership
      file:
        path: "{{ PERF.home_path }}/miniconda3"
        state: directory
        mode: "0755"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        recurse: true
  when:
    - ansible_host in groups.generator
    - conda_installed.rc != 0
  notify:
    - Del miniconda3 installation script
    - Reset SSH Connection

- meta: flush_handlers

- name: Valid git
  block:
    - name: Check if git is installed
      shell: git --version
      register: git_installed
      failed_when: git_installed.rc != 0

  rescue:
    - name: apt install git
      apt:
        update_cache: true
        pkg: git

- name: Manage repository and SSH setup
  block:
    - name: Check if testing repository already exists
      stat:
        path: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
      register: repo_stat
      failed_when: not repo_stat.stat.exists
  rescue:
    - name: Overwrite the SSH keys configuration file
      copy:
        src: "{{ item[0] }}"
        dest: "{{ PERF.home_path }}/.ssh/{{ item[0] | basename }}"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "{{ item[1] }}"
        force: true
        backup: true
      loop:
        - - "keys/{{ PERF.repo.perf.ssh_key_name }}"
          - "0600"
        - - "keys/{{ PERF.repo.perf.ssh_key_name }}.pub"
          - "0644"
        - - "keys/config"
          - "0600"

    - name: Ensure bitbucket directory exists
      file:
        path: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}"
        state: directory
        mode: "0755"

    - name: Clone the target repository
      git:
        repo: "{{ PERF.repo.perf.address }}"
        dest: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
        accept_hostkey: true
      environment:
        GIT_SSH_COMMAND: "ssh -i {{ PERF.home_path }}/.ssh/{{ PERF.repo.perf.ssh_key_name }}"

  always:
    - name: Ensure .ssh directory permissions
      file:
        path: "{{ PERF.home_path }}/.ssh"
        state: directory
        mode: "0700"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"

    - name: Check latest repository status
      stat:
        path: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
      register: latest_repo_stat

    - name: Continue if repository exists
      block:
        - name: Check current branch
          command: "git branch --show-current"
          args:
            chdir: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
          register: current_branch
          environment:
            GIT_SSH_COMMAND: "ssh -i {{ PERF.home_path }}/.ssh/{{ PERF.repo.perf.ssh_key_name }}"
          changed_when: false
          failed_when: false

        - name: Switch to main branch if not already on it
          command: "git switch {{ PERF.repo.perf.target_branch | default('main') }}"
          args:
            chdir: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
          environment:
            GIT_SSH_COMMAND: "ssh -i {{ PERF.home_path }}/.ssh/{{ PERF.repo.perf.ssh_key_name }}"
          when: current_branch.stdout != PERF.repo.perf.target_branch | default('main')

        - name: Pull latest changes if repository exists
          git:
            repo: "{{ PERF.repo.perf.address }}"
            dest: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
            update: true # Equivalent to performing a git pull
            accept_hostkey: true
          environment:
            GIT_SSH_COMMAND: "ssh -i {{ PERF.home_path }}/.ssh/{{ PERF.repo.perf.ssh_key_name }}"
          ignore_errors: true

        - name: Ensure repository directory permissions and ownership
          file:
            path: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}"
            state: directory
            mode: "0755"
            owner: "{{ ansible_user }}"
            group: "{{ ansible_user }}"
            recurse: true
      when: latest_repo_stat.stat.exists

    # - name: Include git config task from Dependencies/RepoOps
    #   include_role:
    #     name: Dependencies/RepoOps
    #     tasks_from: task-git-config

- name: Create locust env with conda
  shell: "{{ PERF.home_path }}/{{ PERF.locust.conda.relative_conda_path }} create -n locust_v{{ PERF.locust.conda.locust_version }} python={{ PERF.locust.conda.python_version }} -y"

- name: Install locust requirements in conda env with pip
  shell:
    chdir: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}/{{ PERF.locust.requirements.relative_dir_path_in_repo }}"
    cmd: "{{ PERF.home_path }}/miniconda3/envs/locust_v{{ PERF.locust.conda.locust_version }}/bin/python -m pip install -r {{ item }}"
    # cmd: "/root/miniconda3/envs/locust_v{{ PERF.locust.conda.locust_version }}/bin/python -m pip install -r {{ item }}"
  loop:
    - locust_v{{ PERF.locust.conda.locust_version }}_requirements.txt
    - requirements.dev.txt
    - requirements.locust.backend.txt
