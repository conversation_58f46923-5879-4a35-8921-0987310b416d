---
# Ensure remote directories exist
- name: Create remote scripts directory
  file:
    path: "{{ remote_scripts_dir }}"
    state: directory
    mode: "0755"
  become: false

# Copy base shell scripts to remote hosts first
- name: Copy base shell scripts to remote hosts
  copy:
    src: "{{ item }}"
    dest: "{{ remote_scripts_dir }}/{{ item }}"
    mode: "0755"
    backup: true
  loop: "{{ MEDICAL.scripts.base_shells }}"
  register: base_scripts_copy_result
  when:
    - MEDICAL.scripts.base_shells is defined
    - MEDICAL.scripts.base_shells | length > 0
    - sync_base_shells | default(true) | bool

# Copy main script to remote hosts
- name: Copy main script to remote hosts
  copy:
    src: "{{ script_name }}"
    dest: "{{ remote_scripts_dir }}/{{ script_name }}"
    mode: "0755"
    backup: true
  register: script_copy_result

# Create execution log
- name: Create execution log entry
  lineinfile:
    path: "{{ remote_scripts_dir }}/execution.log"
    line: "{{ ansible_date_time.iso8601 }} - {{ ansible_hostname }} - {{ script_name }} - STARTED"
    create: true
    mode: "0644"
  become: false

# Execute the script with proper error handling
- name: Execute shell script
  vars:
    timeout_cmd: "{% if MEDICAL.scripts.execution_timeout | int != -1 %}timeout {{ MEDICAL.scripts.execution_timeout }}{% endif %}"
  shell: >
    set -euo pipefail;
    cd "{{ remote_scripts_dir }}";
    LOG_FILE="{{ remote_scripts_dir }}/{{ script_name }}_{{ ansible_date_time.epoch }}.log";
    if {{ timeout_cmd }} bash "{{ remote_scripts_dir }}/{{ script_name }}" "{{ release_tag }}" 2>&1 | tee "$LOG_FILE"; then
      SCRIPT_EXIT_CODE=0;
      echo "Script executed successfully";
    else
      SCRIPT_EXIT_CODE=$?;
      echo "Script execution failed with exit code: $SCRIPT_EXIT_CODE";
    fi;
    exit $SCRIPT_EXIT_CODE
  register: script_execution_result
  become: false
  args:
    executable: /bin/bash
  failed_when: script_execution_result.rc != 0
  changed_when: script_execution_result.rc == 0

# Log successful execution
- name: Log successful execution
  lineinfile:
    path: "{{ remote_scripts_dir }}/execution.log"
    line: "{{ ansible_date_time.iso8601 }} - {{ ansible_hostname }} - {{ script_name }} - SUCCESS - Exit Code: {{ script_execution_result.rc }}"
    mode: "0644"
  become: false
  when: script_execution_result.rc == 0

# Log failed execution
- name: Log failed execution
  lineinfile:
    path: "{{ remote_scripts_dir }}/execution.log"
    line: "{{ ansible_date_time.iso8601 }} - {{ ansible_hostname }} - {{ script_name }} - FAILED - Exit Code: {{ script_execution_result.rc }}"
    mode: "0644"
  become: false
  when: script_execution_result.rc != 0

# Display execution results
- name: Display script execution results
  debug:
    msg: |
      Script Execution Summary:
      ========================
      Script Name: {{ script_name }}
      Target Host: {{ ansible_hostname }}
      Exit Code: {{ script_execution_result.rc }}
      Execution Time: {{ ansible_date_time.iso8601 }}

      Script Output:
      {{ script_execution_result.stdout }}

      {% if script_execution_result.stderr %}
      Script Errors:
      {{ script_execution_result.stderr }}
      {% endif %}

      Log File: {{ remote_scripts_dir }}/{{ script_name }}_{{ ansible_date_time.epoch }}.log

# Handle script execution failure
- name: Handle script execution failure
  fail:
    msg: |
      Script execution failed on host {{ ansible_hostname }}:

      Script: {{ script_name }}
      Exit Code: {{ script_execution_result.rc }}
      Error Output: {{ script_execution_result.stderr | default('No error output') }}

      Check the detailed log at: {{ remote_scripts_dir }}/{{ script_name }}_{{ ansible_date_time.epoch }}.log
  when: script_execution_result.rc != 0

# Clean up old script files (idempotency)
- name: Clean up old script executions (keep last 10)
  shell: |
    cd "{{ remote_scripts_dir }}"
    # Keep only the 10 most recent log files for this script
    ls -1t {{ script_name }}_*.log 2>/dev/null | tail -n +11 | xargs -r rm -f
  become: false
  changed_when: false
  failed_when: false
