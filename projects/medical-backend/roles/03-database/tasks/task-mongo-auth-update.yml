---
# MongoDB Authentication Update Task
# This task performs string replacement in configuration files across multiple servers
# based on the configuration defined in MEDICAL.database.services

# Validate required parameters
- name: Validate required parameters for MongoDB auth update
  fail:
    msg: |
      Missing required parameters for MongoDB authentication update.
      Please provide:
      - old_string: The string to be replaced
      - new_string: The new string to replace with

      Example usage:
      ansible-playbook site.yml -t mongo_auth_update -e "old_string='yakumaru:1111111111111111' new_string='yakumaru-new:2222222222222222'"
  when:
    - (old_string is not defined or old_string == "")
    - (MEDICAL.database.old_string is not defined or MEDICAL.database.old_string == "")

- name: Set string replacement variables
  set_fact:
    mongo_old_string: "{{ old_string | default(MEDICAL.database.old_string) }}"
    mongo_new_string: "{{ new_string | default(MEDICAL.database.new_string) }}"

- name: Display MongoDB auth update summary
  debug:
    msg: |
      MongoDB Authentication Update Summary:
      ====================================
      Old String: {{ mongo_old_string }}
      New String: {{ mongo_new_string }}
      Services to update: {{ MEDICAL.database.services | length }}

      Services list:
      {% for service in MEDICAL.database.services %}
      - {{ service.service_name }}: {{ service.ips | join(', ') }}
      {% endfor %}

# Main processing loop - iterate through each service
- name: Process MongoDB auth update for each service
  include_tasks: task-process-single-service.yml
  loop: "{{ MEDICAL.database.services }}"
  loop_control:
    loop_var: current_service
    label: "{{ current_service.service_name }}"

# Summary report
- name: MongoDB auth update completion summary
  debug:
    msg: |
      MongoDB Authentication Update Completed
      =====================================
      Total services processed: {{ MEDICAL.database.services | length }}
      String replacement: {{ mongo_old_string }} → {{ mongo_new_string }}

      Check individual task results above for detailed status.
