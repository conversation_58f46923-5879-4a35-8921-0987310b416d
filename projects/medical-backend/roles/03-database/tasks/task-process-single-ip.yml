---
# Process single IP - handles file replacement and command execution
# This file is included for each IP. It expects 'current_ip', 'service_files', 'service_command', and 'service_name' from parent.

- name: "IP: {{ current_ip }} | Check server connectivity"
  ping:
  delegate_to: "{{ current_ip }}"
  register: ping_result
  failed_when: false

- name: "IP: {{ current_ip }} | Skip processing if server unreachable"
  debug:
    msg: "Warning: Server {{ current_ip }} is unreachable. Skipping file processing for this IP."
  when: ping_result.failed | default(false)

- name: "IP: {{ current_ip }} | Process files for reachable server"
  block:
    - name: "IP: {{ current_ip }} | Display file processing info"
      debug:
        msg: |
          Processing IP: {{ current_ip }} ({{ service_name }})
          ============================================
          Files to process: {{ service_files | join(', ') }}
          String replacement: {{ mongo_old_string }} → {{ mongo_new_string }}

    # Process each file on the current IP
    - name: "IP: {{ current_ip }} | Process string replacement in each file"
      include_tasks: task-process-single-file.yml
      loop: "{{ service_files }}"
      loop_control:
        loop_var: current_file
        label: "{{ current_ip }}:{{ current_file }}"

    # Execute post-replacement command if specified
    - name: "IP: {{ current_ip }} | Execute post-replacement command"
      shell: "{{ service_command }}"
      register: command_result
      failed_when: false
      delegate_to: "{{ current_ip }}"
      when:
        - service_command is defined
        - service_command != ""
        - service_command | trim != ""

    - name: "IP: {{ current_ip }} | Display command execution result"
      debug:
        msg: |
          Command Execution Result for {{ current_ip }}:
          =============================================
          Command: {{ service_command }}
          Exit Code: {{ command_result.rc | default('N/A') }}
          {% if command_result.rc is defined %}
          Status: {{ 'SUCCESS' if command_result.rc == 0 else 'FAILED' }}
          {% if command_result.stdout %}
          Output: {{ command_result.stdout }}
          {% endif %}
          {% if command_result.stderr %}
          Error: {{ command_result.stderr }}
          {% endif %}
          {% else %}
          Status: No command to execute
          {% endif %}
      when:
        - service_command is defined
        - service_command != ""
        - service_command | trim != ""

    - name: "IP: {{ current_ip }} | Warning about command failure"
      debug:
        msg: |
          WARNING: Command execution failed on {{ current_ip }}
          This will not stop processing of other servers.
          Command: {{ service_command }}
          Exit Code: {{ command_result.rc }}
          Error: {{ command_result.stderr | default('No error output') }}
      when:
        - service_command is defined
        - service_command != ""
        - service_command | trim != ""
        - command_result.rc is defined
        - command_result.rc != 0

  when: not (ping_result.failed | default(false))
