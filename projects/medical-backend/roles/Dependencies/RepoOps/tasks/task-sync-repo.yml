---
- name: Ensure SSH directory exists
  file:
    path: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/.ssh"
    state: directory
    mode: "0700"

- name: Check if repository directory exists
  stat:
    path: "{{ MEDICAL.home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"
  register: repo_dir_stat

- name: Create repository root directory if not exists
  file:
    path: "{{ MEDICAL.home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"
    state: directory
    mode: "0755"
  when: not repo_dir_stat.stat.exists

- name: Check if git repository is already cloned
  stat:
    path: "{{ MEDICAL.home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}/.git"
  register: git_repo_stat

- name: Clone repository if not exists
  git:
    repo: "{{ MEDICAL.repo.medical.address }}"
    dest: "{{ MEDICAL.home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"
    version: "{{ MEDICAL.repo.medical.target_branch }}"
    key_file: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/.ssh/{{ MEDICAL.repo.medical.ssh_key_name }}"
  when: not git_repo_stat.stat.exists

- name: Pull latest changes from repository
  git:
    repo: "{{ MEDICAL.repo.medical.address }}"
    dest: "{{ MEDICAL.home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"
    version: "{{ MEDICAL.repo.medical.target_branch }}"
    key_file: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/.ssh/{{ MEDICAL.repo.medical.ssh_key_name }}"
    force: true
  when: git_repo_stat.stat.exists

- name: Set repository directory permissions
  file:
    path: "{{ MEDICAL.home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"
    state: directory
    mode: "0755"
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    recurse: true
