---
- name: Ensure git is installed
  package:
    name: git
    state: present
  become: true

- name: Configure git user name
  git_config:
    name: user.name
    scope: global
    value: "Medical Backend Automation"

- name: Configure git user email
  git_config:
    name: user.email
    scope: global
    value: "<EMAIL>"

- name: Configure git to trust the repository directory
  git_config:
    name: safe.directory
    scope: global
    value: "{{ MEDICAL.home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"

- name: Configure git credential helper timeout
  git_config:
    name: credential.helper
    scope: global
    value: "cache --timeout=3600"
